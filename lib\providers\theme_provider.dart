import 'package:flutter/material.dart';
import 'package:hive_flutter/hive_flutter.dart';

class ThemeProvider extends ChangeNotifier {
  bool _isDarkMode = false;
  late Box _box;

  bool get isDarkMode => _isDarkMode;

  ThemeProvider() {
    _loadTheme();
  }

  void _loadTheme() async {
    _box = Hive.box('mybox');
    _isDarkMode = _box.get('dark_mode', defaultValue: false);
    notifyListeners();
  }

  void toggleTheme() {
    _isDarkMode = !_isDarkMode;
    _box.put('dark_mode', _isDarkMode);
    notifyListeners();
  }

  // Light Theme
  ThemeData get lightTheme {
    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.light,
      colorScheme: ColorScheme.fromSeed(
        seedColor: Colors.yellow,
        brightness: Brightness.light,
      ),
      appBarTheme: AppBarTheme(
        centerTitle: false,
        titleTextStyle: const TextStyle(
          fontSize: 20,
          fontWeight: FontWeight.w600,
          color: Colors.black,
        ),
        backgroundColor: Colors.yellow[50],
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.black),
      ),
      scaffoldBackgroundColor: Colors.yellow[50],
      cardTheme: CardThemeData(
        color: Colors.white,
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
      floatingActionButtonTheme: FloatingActionButtonThemeData(
        backgroundColor: Colors.yellow[600],
        foregroundColor: Colors.black,
      ),
      bottomNavigationBarTheme: BottomNavigationBarThemeData(
        backgroundColor: Colors.white,
        selectedItemColor: Colors.yellow[700],
        unselectedItemColor: Colors.grey[600],
        elevation: 8,
      ),
      dialogTheme: DialogThemeData(
        backgroundColor: Colors.yellow[100],
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
      ),
      // Custom colors for task states
      extensions: <ThemeExtension<dynamic>>[
        TaskColors.light,
      ],
    );
  }

  // Dark Theme
  ThemeData get darkTheme {
    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.dark,
      colorScheme: ColorScheme.fromSeed(
        seedColor: Colors.yellow,
        brightness: Brightness.dark,
      ),
      appBarTheme: AppBarTheme(
        centerTitle: false,
        titleTextStyle: const TextStyle(
          fontSize: 20,
          fontWeight: FontWeight.w600,
          color: Colors.white,
        ),
        backgroundColor: Colors.grey[900],
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
      ),
      scaffoldBackgroundColor: Colors.grey[900],
      cardTheme: CardThemeData(
        color: Colors.grey[800],
        elevation: 4,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
      floatingActionButtonTheme: FloatingActionButtonThemeData(
        backgroundColor: Colors.yellow[600],
        foregroundColor: Colors.black,
      ),
      bottomNavigationBarTheme: BottomNavigationBarThemeData(
        backgroundColor: Colors.grey[850],
        selectedItemColor: Colors.yellow[400],
        unselectedItemColor: Colors.grey[400],
        elevation: 8,
      ),
      dialogTheme: DialogThemeData(
        backgroundColor: Colors.grey[800],
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
      ),
      // Custom colors for task states
      extensions: <ThemeExtension<dynamic>>[
        TaskColors.dark,
      ],
    );
  }
}

// Custom theme extension for task-specific colors
@immutable
class TaskColors extends ThemeExtension<TaskColors> {
  const TaskColors({
    required this.normalTaskBackground,
    required this.normalTaskText,
    required this.overdueTaskBackground,
    required this.overdueTaskText,
    required this.overdueTaskBorder,
    required this.completedTaskBackground,
    required this.completedTaskText,
    required this.homeBackground,
    required this.pendingBackground,
    required this.completedBackground,
    required this.settingsBackground,
    required this.statsBackground,
    required this.statsBorder,
  });

  final Color normalTaskBackground;
  final Color normalTaskText;
  final Color overdueTaskBackground;
  final Color overdueTaskText;
  final Color overdueTaskBorder;
  final Color completedTaskBackground;
  final Color completedTaskText;
  final Color homeBackground;
  final Color pendingBackground;
  final Color completedBackground;
  final Color settingsBackground;
  final Color statsBackground;
  final Color statsBorder;

  // Light theme colors
  static const TaskColors light = TaskColors(
    normalTaskBackground: Color(0xFFFFF176), // Yellow[300]
    normalTaskText: Colors.black,
    overdueTaskBackground: Color(0xFFFFCDD2), // Red[100]
    overdueTaskText: Color(0xFFD32F2F), // Red[700]
    overdueTaskBorder: Color(0xFFD32F2F), // Red[700]
    completedTaskBackground: Color(0xFFC8E6C9), // Green[100]
    completedTaskText: Colors.black,
    homeBackground: Color(0xFFFFFDE7), // Yellow[50]
    pendingBackground: Color(0xFFFFFDE7), // Yellow[50]
    completedBackground: Color(0xFFE8F5E8), // Green[50]
    settingsBackground: Color(0xFFFFFDE7), // Yellow[50] to match other pages
    statsBackground: Color(0xFFC8E6C9), // Green[100]
    statsBorder: Color(0xFF81C784), // Green[300]
  );

  // Dark theme colors
  static const TaskColors dark = TaskColors(
    normalTaskBackground: Color(0xFF555555), // Dark grey
    normalTaskText: Colors.white,
    overdueTaskBackground: Color(0xFF5D2D2D), // Dark red
    overdueTaskText: Color(0xFFFF5252), // Light red
    overdueTaskBorder: Color(0xFFFF5252), // Light red
    completedTaskBackground: Color(0xFF2D5D2D), // Dark green
    completedTaskText: Colors.white,
    homeBackground: Color(0xFF121212), // Very dark
    pendingBackground: Color(0xFF121212), // Very dark
    completedBackground: Color(0xFF1A1A1A), // Dark
    settingsBackground: Color(0xFF1A1A1A), // Dark
    statsBackground: Color(0xFF2D5D2D), // Dark green
    statsBorder: Color(0xFF4CAF50), // Green
  );

  @override
  TaskColors copyWith({
    Color? normalTaskBackground,
    Color? normalTaskText,
    Color? overdueTaskBackground,
    Color? overdueTaskText,
    Color? overdueTaskBorder,
    Color? completedTaskBackground,
    Color? completedTaskText,
    Color? homeBackground,
    Color? pendingBackground,
    Color? completedBackground,
    Color? settingsBackground,
    Color? statsBackground,
    Color? statsBorder,
  }) {
    return TaskColors(
      normalTaskBackground: normalTaskBackground ?? this.normalTaskBackground,
      normalTaskText: normalTaskText ?? this.normalTaskText,
      overdueTaskBackground: overdueTaskBackground ?? this.overdueTaskBackground,
      overdueTaskText: overdueTaskText ?? this.overdueTaskText,
      overdueTaskBorder: overdueTaskBorder ?? this.overdueTaskBorder,
      completedTaskBackground: completedTaskBackground ?? this.completedTaskBackground,
      completedTaskText: completedTaskText ?? this.completedTaskText,
      homeBackground: homeBackground ?? this.homeBackground,
      pendingBackground: pendingBackground ?? this.pendingBackground,
      completedBackground: completedBackground ?? this.completedBackground,
      settingsBackground: settingsBackground ?? this.settingsBackground,
      statsBackground: statsBackground ?? this.statsBackground,
      statsBorder: statsBorder ?? this.statsBorder,
    );
  }

  @override
  TaskColors lerp(ThemeExtension<TaskColors>? other, double t) {
    if (other is! TaskColors) {
      return this;
    }
    return TaskColors(
      normalTaskBackground: Color.lerp(normalTaskBackground, other.normalTaskBackground, t)!,
      normalTaskText: Color.lerp(normalTaskText, other.normalTaskText, t)!,
      overdueTaskBackground: Color.lerp(overdueTaskBackground, other.overdueTaskBackground, t)!,
      overdueTaskText: Color.lerp(overdueTaskText, other.overdueTaskText, t)!,
      overdueTaskBorder: Color.lerp(overdueTaskBorder, other.overdueTaskBorder, t)!,
      completedTaskBackground: Color.lerp(completedTaskBackground, other.completedTaskBackground, t)!,
      completedTaskText: Color.lerp(completedTaskText, other.completedTaskText, t)!,
      homeBackground: Color.lerp(homeBackground, other.homeBackground, t)!,
      pendingBackground: Color.lerp(pendingBackground, other.pendingBackground, t)!,
      completedBackground: Color.lerp(completedBackground, other.completedBackground, t)!,
      settingsBackground: Color.lerp(settingsBackground, other.settingsBackground, t)!,
      statsBackground: Color.lerp(statsBackground, other.statsBackground, t)!,
      statsBorder: Color.lerp(statsBorder, other.statsBorder, t)!,
    );
  }
}
