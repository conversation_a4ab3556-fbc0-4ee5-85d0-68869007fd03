import 'package:flutter/material.dart';
import '../data/database.dart';
import '../providers/theme_provider.dart';
import 'util/dialog_box.dart';
import 'util/todo_tile.dart';

class PendingTasksPage extends StatefulWidget {
  final ToDoDatabase database;
  final VoidCallback onDataChanged;
  final bool showOnlyHome;

  const PendingTasksPage({
    super.key,
    required this.database,
    required this.onDataChanged,
    required this.showOnlyHome,
  });

  @override
  State<PendingTasksPage> createState() => _PendingTasksPageState();
}

class _PendingTasksPageState extends State<PendingTasksPage> {
  final _controller = TextEditingController();
  DateTime? _selectedDueDate;
  DateTime? _selectedDueTime;

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  // Get tasks based on page type
  List<ToDoTask> _getTasks() {
    final pendingTasks = widget.database.getPendingTasks();
    if (widget.showOnlyHome) {
      // For home page, show tasks due within the next 7 days or overdue
      final now = DateTime.now();
      final weekFromNow = now.add(Duration(days: 7));
      return pendingTasks.where((task) {
        return task.isOverdue || task.combinedDueDateTime.isBefore(weekFromNow);
      }).toList();
    }
    return pendingTasks;
  }

  // CheckBox was tapped
  void checkBoxChanged(bool? value, int taskIndex) {
    final tasks = _getTasks();
    if (taskIndex < tasks.length) {
      final task = tasks[taskIndex];
      final originalIndex = widget.database.toDoList.indexOf(task);
      
      setState(() {
        widget.database.toDoList[originalIndex].isCompleted = !widget.database.toDoList[originalIndex].isCompleted;
        if (widget.database.toDoList[originalIndex].isCompleted) {
          widget.database.toDoList[originalIndex].completedDate = DateTime.now();
        } else {
          widget.database.toDoList[originalIndex].completedDate = null;
        }
      });
      widget.database.updateDatabase();
      widget.onDataChanged();
    }
  }

  // Save new task
  void saveNewTask() {
    if (_controller.text.trim().isEmpty) return;
    
    final dueDate = _selectedDueDate ?? DateTime.now().add(Duration(days: 1));
    
    setState(() {
      widget.database.toDoList.add(ToDoTask(
        name: _controller.text.trim(),
        isCompleted: false,
        dueDate: dueDate,
        dueTime: _selectedDueTime,
      ));
      _controller.clear();
      _selectedDueDate = null;
      _selectedDueTime = null;
    });
    Navigator.of(context).pop();
    widget.database.updateDatabase();
    widget.onDataChanged();
  }

  // Save edited task
  void saveEditedTask(int taskIndex) {
    if (_controller.text.trim().isEmpty) return;
    
    final tasks = _getTasks();
    if (taskIndex < tasks.length) {
      final task = tasks[taskIndex];
      final originalIndex = widget.database.toDoList.indexOf(task);
      
      final dueDate = _selectedDueDate ?? task.dueDate;
      
      setState(() {
        widget.database.toDoList[originalIndex].name = _controller.text.trim();
        widget.database.toDoList[originalIndex].dueDate = dueDate;
        widget.database.toDoList[originalIndex].dueTime = _selectedDueTime;
      });
      Navigator.of(context).pop();
      _controller.clear();
      _selectedDueDate = null;
      _selectedDueTime = null;
      widget.database.updateDatabase();
      widget.onDataChanged();
    }
  }

  // Create a new task
  void createNewTask() {
    _selectedDueDate = DateTime.now().add(Duration(days: 1));
    _selectedDueTime = null;
    
    showDialog(
      context: context,
      builder: (context) {
        return DialogBox(
          controller: _controller,
          onSave: saveNewTask,
          onCancel: () {
            _controller.clear();
            _selectedDueDate = null;
            _selectedDueTime = null;
            Navigator.of(context).pop();
          },
          title: 'New Task',
          hintText: 'Add a new task',
          initialDueDate: _selectedDueDate,
          initialDueTime: _selectedDueTime,
          onDueDateChanged: (date) => _selectedDueDate = date,
          onDueTimeChanged: (time) => _selectedDueTime = time,
        );
      },
    );
  }

  // Delete task
  void deleteTask(int taskIndex) {
    final tasks = _getTasks();
    if (taskIndex < tasks.length) {
      final task = tasks[taskIndex];
      final originalIndex = widget.database.toDoList.indexOf(task);
      
      setState(() {
        widget.database.toDoList.removeAt(originalIndex);
      });
      widget.database.updateDatabase();
      widget.onDataChanged();
    }
  }

  // Edit task
  void editTask(int taskIndex) {
    final tasks = _getTasks();
    if (taskIndex < tasks.length) {
      final task = tasks[taskIndex];
      _controller.text = task.name;
      _selectedDueDate = task.dueDate;
      _selectedDueTime = task.dueTime;
      
      showDialog(
        context: context,
        builder: (context) {
          return DialogBox(
            controller: _controller,
            onSave: () => saveEditedTask(taskIndex),
            onCancel: () {
              _controller.clear();
              _selectedDueDate = null;
              _selectedDueTime = null;
              Navigator.of(context).pop();
            },
            title: 'Edit Task',
            hintText: 'Edit your task',
            initialDueDate: _selectedDueDate,
            initialDueTime: _selectedDueTime,
            onDueDateChanged: (date) => _selectedDueDate = date,
            onDueTimeChanged: (time) => _selectedDueTime = time,
          );
        },
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final tasks = _getTasks();
    final title = widget.showOnlyHome ? 'Home' : 'Pending Tasks';
    final taskColors = Theme.of(context).extension<TaskColors>()!;
    final theme = Theme.of(context);
    
    return Scaffold(
      backgroundColor: widget.showOnlyHome 
          ? taskColors.homeBackground 
          : taskColors.pendingBackground,
      appBar: AppBar(
        title: Text(title),
        actions: [
          if (tasks.isNotEmpty)
            Padding(
              padding: const EdgeInsets.only(right: 16.0),
              child: Center(
                child: Text(
                  '${tasks.length} tasks',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: createNewTask,
        child: const Icon(Icons.add),
      ),
      body: tasks.isEmpty
          ? Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.task_alt,
                    size: 64,
                    color: Colors.grey[400],
                  ),
                  const SizedBox(height: 16),
                  Text(
                    widget.showOnlyHome 
                        ? 'No tasks for the next week'
                        : 'No pending tasks',
                    style: TextStyle(
                      fontSize: 18,
                      color: Colors.grey[600],
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Tap the + button to add a task',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[500],
                    ),
                  ),
                ],
              ),
            )
          : ListView.builder(
              itemCount: tasks.length,
              itemBuilder: (context, index) {
                return ToDoTile(
                  task: tasks[index],
                  onChanged: (value) => checkBoxChanged(value, index),
                  deleteFunction: (context) => deleteTask(index),
                  editFunction: (context) => editTask(index),
                );
              },
            ),
    );
  }
}
