# WARP.md

This file provides guidance to WA<PERSON> (warp.dev) when working with code in this repository.

## Project Overview

This is "My To Do" - a comprehensive Flutter task management application with advanced features. The app includes date-based task organization, multiple views, and local data persistence using Hive. Key features include:

- **Date & Time Management**: Mandatory due dates with optional times for precise scheduling
- **Smart Organization**: Tasks sorted by due date/time with overdue highlighting
- **Multi-Tab Navigation**: Home, Pending, Completed, and Settings views
- **Visual Status Indicators**: Color-coded tasks (yellow=normal, red=overdue, green=completed)
- **Task Lifecycle**: Automatic movement from pending to completed tabs when marked done

## Essential Development Commands

### Setup and Dependencies
```bash
# Get all dependencies
flutter pub get

# Generate code for Hive (if needed in future)
flutter packages pub run build_runner build
```

### Build and Run
```bash
# Run on connected device/emulator
flutter run

# Run in debug mode with hot reload
flutter run --debug

# Build for specific platforms
flutter build apk                    # Android APK
flutter build appbundle            # Android App Bundle
flutter build ios                  # iOS (requires Xcode)
flutter build windows              # Windows desktop
flutter build web                  # Web application
```

### Testing and Quality
```bash
# Run all tests
flutter test

# Run specific test file
flutter test test/widget_test.dart

# Analyze code for issues
flutter analyze

# Format code
flutter format lib/
```

### Platform-Specific Commands
```bash
# Clean build artifacts
flutter clean

# Check Flutter and environment setup
flutter doctor

# List connected devices
flutter devices
```

## Architecture Overview

### Data Layer
- **Database**: `lib/data/database.dart` - `ToDoDatabase` class with enhanced `ToDoTask` model
- **Storage**: Uses Hive local database with automatic legacy data conversion
- **Data Structure**: 
  ```dart
  class ToDoTask {
    String name;
    bool isCompleted;
    DateTime dueDate;        // Mandatory due date
    DateTime? dueTime;       // Optional time component
    DateTime? completedDate; // Auto-set when task completed
  }
  ```

### UI Layer Structure
```
lib/
├── main.dart                    # App entry point, Material3 theme
├── pages/
│   ├── main_navigation.dart     # Bottom navigation with 4 tabs
│   ├── pending_tasks_page.dart  # Home & Pending tasks (with filtering)
│   ├── completed_tasks_page.dart# Completed tasks with statistics
│   ├── settings_page.dart       # App settings and data management
│   └── util/                    # Enhanced UI components
│       ├── todo_tile.dart       # Rich task display with date/status
│       ├── dialog_box.dart      # Date/time picker dialog
│       └── my_button.dart       # Custom button component
└── data/
    └── database.dart            # Enhanced database with sorting/filtering
```

### Key Dependencies
- **hive_flutter**: Local NoSQL database for data persistence
- **flutter_slidable**: Swipe-to-delete functionality for tasks
- **Material Design**: Uses yellow color scheme throughout

### State Management
- Uses StatefulWidget with setState() for local state management
- Database operations automatically trigger UI updates via setState()
- No external state management solution (Provider, Bloc, etc.)

### Data Flow
1. App initializes Hive box and loads `MainNavigation`
2. `ToDoDatabase` loads existing data with legacy format conversion
3. Bottom navigation manages 4 tabs: Home, Pending, Completed, Settings
4. **Home Tab**: Shows tasks due within 7 days + overdue tasks
5. **Pending Tab**: Shows all incomplete tasks sorted by due date/time
6. **Completed Tab**: Shows finished tasks with completion dates and statistics
7. Task interactions update database and trigger cross-tab refresh via callbacks
8. Visual indicators: Yellow (normal), Red (overdue), Green (completed)

### Testing Structure
- Widget tests for individual components (MyButton, DialogBox, ToDoTile)
- Tests focus on component functionality rather than full app integration
- Avoids Hive plugin dependencies that cause issues in test environment

## Development Notes

### Recent Improvements
- ✅ **Complete UI Overhaul**: 4-tab bottom navigation (Home, Pending, Completed, Settings)
- ✅ **Date & Time Management**: Mandatory due dates + optional times with native pickers
- ✅ **Smart Task Organization**: Auto-sorting by due date/time across all views
- ✅ **Visual Status System**: Color-coded tasks with overdue highlighting (red borders/text)
- ✅ **Enhanced Task Display**: Shows due dates, completion dates, and overdue warnings
- ✅ **Completed Tasks Tracking**: Separate tab with statistics (today/week/total)
- ✅ **Settings & Data Management**: App preferences and data clearing options
- ✅ **Legacy Data Migration**: Automatic conversion from old task format
- ✅ **Material Design 3**: Updated theme with improved visual hierarchy
- ✅ **Dark/Light Mode**: Complete theme system with persistent settings
- ✅ **Custom Branding**: App logo integration with custom splash screen
- ✅ **Multi-Platform Icons**: Auto-generated app icons for Android, iOS, Web, Windows, macOS

### Key Features Usage

**Creating Tasks:**
- Tap floating + button → Select due date (mandatory) → Optionally add time → Save
- Tasks automatically sort by due date/time in all views

**Task Management:**
- **Home Tab**: Quick view of tasks due within 7 days + overdue items
- **Pending Tab**: Complete list of all unfinished tasks
- **Swipe Actions**: Edit (green) and Delete (red) via right swipe
- **Visual Cues**: Red borders/text for overdue, yellow for normal, green for completed

**Completion Tracking:**
- Check tasks → Automatically moves to Completed tab with timestamp
- **Completed Tab**: Shows due date, completion date, and statistics
- Uncheck completed tasks to move back to pending

### Extension Points
- Task categories/priorities system
- Search/filter functionality within tabs
- Notification system for due dates
- Export/import functionality (CSV, JSON)
- Task reordering with drag and drop
- Recurring tasks support
- Task attachments/notes
