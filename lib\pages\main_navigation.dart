import 'package:flutter/material.dart';
import 'package:hive_flutter/hive_flutter.dart';
import '../data/database.dart';
import 'pending_tasks_page.dart';
import 'completed_tasks_page.dart';
import 'settings_page.dart';

class MainNavigation extends StatefulWidget {
  const MainNavigation({super.key});

  @override
  State<MainNavigation> createState() => _MainNavigationState();
}

class _MainNavigationState extends State<MainNavigation> {
  int _currentIndex = 0;
  late ToDoDatabase db;
  late Box _myBox;

  @override
  void initState() {
    super.initState();
    _myBox = Hive.box('mybox');
    db = ToDoDatabase();
    
    if (_myBox.get("TODOLIST") == null) {
      db.createInitialData();
      db.updateDatabase();
    } else {
      db.loadData();
    }
  }

  void _refreshData() {
    setState(() {
      db.loadData();
    });
  }

  List<Widget> get _pages => [
    PendingTasksPage(
      database: db,
      onDataChanged: _refreshData,
      showOnlyHome: true,
    ),
    PendingTasksPage(
      database: db,
      onDataChanged: _refreshData,
      showOnlyHome: false,
    ),
    CompletedTasksPage(database: db),
    const SettingsPage(),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: IndexedStack(
        index: _currentIndex,
        children: _pages,
      ),
      bottomNavigationBar: BottomNavigationBar(
        type: BottomNavigationBarType.fixed,
        currentIndex: _currentIndex,
        onTap: (index) {
          setState(() {
            _currentIndex = index;
          });
          _refreshData(); // Refresh data when switching tabs
        },
        selectedItemColor: Colors.yellow[700],
        unselectedItemColor: Colors.grey,
        items: const [
          BottomNavigationBarItem(
            icon: Icon(Icons.home),
            label: 'Home',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.pending_actions),
            label: 'Pending',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.check_circle),
            label: 'Completed',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.settings),
            label: 'Settings',
          ),
        ],
      ),
    );
  }
}
