// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:to_do/pages/util/my_button.dart';
import 'package:to_do/pages/util/dialog_box.dart';
import 'package:to_do/pages/util/todo_tile.dart';
import 'package:to_do/data/database.dart';
import 'package:to_do/providers/theme_provider.dart';

void main() {
  testWidgets('MyButton widget test', (WidgetTester tester) async {
    bool wasPressed = false;
    
    await tester.pumpWidget(
      MaterialApp(
        home: Scaffold(
          body: MyButton(
            text: 'Test Button',
            onPressed: () {
              wasPressed = true;
            },
          ),
        ),
      ),
    );

    // Verify button text
    expect(find.text('Test Button'), findsOneWidget);
    
    // Tap the button
    await tester.tap(find.text('Test Button'));
    await tester.pump();
    
    // Verify callback was called
    expect(wasPressed, true);
  });

  testWidgets('DialogBox widget test', (WidgetTester tester) async {
    final controller = TextEditingController();
    bool savePressed = false;
    DateTime? selectedDate;
    DateTime? selectedTime;

    await tester.pumpWidget(
      MaterialApp(
        home: Scaffold(
          body: DialogBox(
            controller: controller,
            onSave: () => savePressed = true,
            onCancel: () {},
            title: 'Test Dialog',
            hintText: 'Test hint',
            onDueDateChanged: (date) => selectedDate = date,
            onDueTimeChanged: (time) => selectedTime = time,
          ),
        ),
      ),
    );

    // Verify dialog elements
    expect(find.text('Test Dialog'), findsOneWidget);
    expect(find.text('Save'), findsOneWidget);
    expect(find.text('Cancel'), findsOneWidget);
    
    // Test text input
    await tester.enterText(find.byType(TextField), 'Test task');
    await tester.pump();
    expect(controller.text, 'Test task');
    
    // Test save button
    await tester.tap(find.text('Save'));
    await tester.pump();
    expect(savePressed, true);
  });

  testWidgets('ToDoTile widget test', (WidgetTester tester) async {
    bool checkboxChanged = false;
    
    final testTask = ToDoTask(
      name: 'Test Task',
      isCompleted: false,
      dueDate: DateTime.now().add(Duration(days: 1)),
    );

    await tester.pumpWidget(
      MaterialApp(
        theme: ThemeData(
          extensions: <ThemeExtension<dynamic>>[
            TaskColors.light,
          ],
        ),
        home: Scaffold(
          body: ToDoTile(
            task: testTask,
            onChanged: (value) => checkboxChanged = true,
            deleteFunction: (context) {},
            editFunction: (context) {},
          ),
        ),
      ),
    );

    // Verify task name
    expect(find.text('Test Task'), findsOneWidget);
    
    // Verify checkbox
    expect(find.byType(Checkbox), findsOneWidget);
    
    // Test checkbox tap
    await tester.tap(find.byType(Checkbox));
    await tester.pump();
    expect(checkboxChanged, true);
  });
}
