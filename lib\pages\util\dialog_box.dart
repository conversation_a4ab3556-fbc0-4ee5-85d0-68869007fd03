import 'package:flutter/material.dart';
import 'package:to_do/pages/util/my_button.dart';

class DialogBox extends StatefulWidget {
  final dynamic controller;
  final VoidCallback onSave;
  final VoidCallback onCancel;
  final String title;
  final String hintText;
  final DateTime? initialDueDate;
  final DateTime? initialDueTime;
  final Function(DateTime)? onDueDateChanged;
  final Function(DateTime?)? onDueTimeChanged;

  const DialogBox({
    super.key,
    required this.controller,
    required this.onSave,
    required this.onCancel,
    this.title = 'New Task',
    this.hintText = 'Add a new task',
    this.initialDueDate,
    this.initialDueTime,
    this.onDueDateChanged,
    this.onDueTimeChanged,
  });

  @override
  State<DialogBox> createState() => _DialogBoxState();
}

class _DialogBoxState extends State<DialogBox> {
  late DateTime selectedDueDate;
  DateTime? selectedDueTime;

  @override
  void initState() {
    super.initState();
    selectedDueDate = widget.initialDueDate ?? DateTime.now().add(Duration(days: 1));
    selectedDueTime = widget.initialDueTime;
  }

  Future<void> _selectDueDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: selectedDueDate,
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(Duration(days: 365)),
    );
    if (picked != null && picked != selectedDueDate) {
      setState(() {
        selectedDueDate = picked;
      });
      widget.onDueDateChanged?.call(picked);
    }
  }

  Future<void> _selectDueTime() async {
    final TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime: selectedDueTime != null 
          ? TimeOfDay.fromDateTime(selectedDueTime!) 
          : TimeOfDay.now(),
    );
    if (picked != null) {
      final DateTime timeAsDateTime = DateTime(
        selectedDueDate.year,
        selectedDueDate.month,
        selectedDueDate.day,
        picked.hour,
        picked.minute,
      );
      setState(() {
        selectedDueTime = timeAsDateTime;
      });
      widget.onDueTimeChanged?.call(timeAsDateTime);
    }
  }

  void _clearTime() {
    setState(() {
      selectedDueTime = null;
    });
    widget.onDueTimeChanged?.call(null);
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  String _formatTime(DateTime time) {
    final hour = time.hour.toString().padLeft(2, '0');
    final minute = time.minute.toString().padLeft(2, '0');
    return '$hour:$minute';
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(widget.title),
      content: SizedBox(
        height: 280,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            // Task name input
            TextField(
              controller: widget.controller,
              decoration: InputDecoration(
                border: OutlineInputBorder(),
                hintText: widget.hintText,
              ),
            ),
            const SizedBox(height: 16),
            
            // Due date picker
            Container(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _selectDueDate,
                icon: Icon(Icons.calendar_today),
                label: Text('Due: ${_formatDate(selectedDueDate)}'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Theme.of(context).colorScheme.primary,
                  foregroundColor: Theme.of(context).colorScheme.onPrimary,
                ),
              ),
            ),
            
            // Due time picker (optional)
            Container(
              width: double.infinity,
              child: selectedDueTime == null
                  ? ElevatedButton.icon(
                      onPressed: _selectDueTime,
                      icon: Icon(Icons.access_time),
                      label: Text('Add Time (Optional)'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Theme.of(context).colorScheme.surfaceVariant,
                        foregroundColor: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                    )
                  : Row(
                      children: [
                        Expanded(
                          child: ElevatedButton.icon(
                            onPressed: _selectDueTime,
                            icon: Icon(Icons.access_time),
                            label: Text('Time: ${_formatTime(selectedDueTime!)}'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Theme.of(context).colorScheme.primary,
                              foregroundColor: Theme.of(context).colorScheme.onPrimary,
                            ),
                          ),
                        ),
                        const SizedBox(width: 8),
                        IconButton(
                          onPressed: _clearTime,
                          icon: Icon(Icons.clear),
                          tooltip: 'Remove time',
                        ),
                      ],
                    ),
            ),
            
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                //Save button
                MyButton(text: "Save", onPressed: widget.onSave),
                const SizedBox(width: 8),
                //Cancel button
                MyButton(text: "Cancel", onPressed: widget.onCancel),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
