import 'package:flutter/material.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import '../../data/database.dart';
import '../../providers/theme_provider.dart';

class ToDoTile extends StatelessWidget {
  final ToDoTask task;
  final Function(bool?)? onChanged;
  final Function(BuildContext)? deleteFunction;
  final Function(BuildContext)? editFunction;
  final bool showCompletionDate;

  const ToDoTile({
    super.key,
    required this.task,
    required this.onChanged,
    required this.deleteFunction,
    required this.editFunction,
    this.showCompletionDate = false,
  });

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  String _formatTime(DateTime time) {
    final hour = time.hour.toString().padLeft(2, '0');
    final minute = time.minute.toString().padLeft(2, '0');
    return '$hour:$minute';
  }

  String _formatDateTime(DateTime date, DateTime? time) {
    if (time != null) {
      return '${_formatDate(date)} at ${_formatTime(time)}';
    }
    return _formatDate(date);
  }

  Color _getContainerColor(TaskColors taskColors) {
    if (task.isCompleted) {
      return taskColors.completedTaskBackground;
    } else if (task.isOverdue) {
      return taskColors.overdueTaskBackground;
    }
    return taskColors.normalTaskBackground;
  }

  Color _getTextColor(TaskColors taskColors) {
    if (task.isOverdue && !task.isCompleted) {
      return taskColors.overdueTaskText;
    } else if (task.isCompleted) {
      return taskColors.completedTaskText;
    }
    return taskColors.normalTaskText;
  }

  @override
  Widget build(BuildContext context) {
    final taskColors = Theme.of(context).extension<TaskColors>()!;
    final colorScheme = Theme.of(context).colorScheme;
    
    return Padding(
      padding: const EdgeInsets.only(left: 24.0, right: 24, top: 24),
      child: Slidable(
        endActionPane: ActionPane(
          motion: const StretchMotion(),
          children: [
            SlidableAction(
              onPressed: deleteFunction,
              icon: Icons.delete,
              backgroundColor: colorScheme.error,
              foregroundColor: colorScheme.onError,
              borderRadius: BorderRadius.circular(12),
            ),
            if (!task.isCompleted) // Only show edit for pending tasks
              SlidableAction(
                onPressed: editFunction,
                icon: Icons.edit,
                backgroundColor: colorScheme.secondary,
                foregroundColor: colorScheme.onSecondary,
                borderRadius: BorderRadius.circular(12),
              ),
          ],
        ),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: _getContainerColor(taskColors),
            borderRadius: BorderRadius.circular(12),
            border: task.isOverdue && !task.isCompleted
                ? Border.all(color: taskColors.overdueTaskBorder, width: 2)
                : null,
          ),
          child: Row(
            children: [
              // Checkbox
              Checkbox(
                value: task.isCompleted,
                onChanged: onChanged,
                activeColor: task.isOverdue 
                    ? taskColors.overdueTaskBorder 
                    : colorScheme.primary,
              ),
              // Task content
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Task name
                    Text(
                      task.name,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                        color: _getTextColor(taskColors),
                        decoration: task.isCompleted
                            ? TextDecoration.lineThrough
                            : TextDecoration.none,
                      ),
                    ),
                    const SizedBox(height: 4),
                    // Due date/time
                    Row(
                      children: [
                        Icon(
                          Icons.schedule,
                          size: 16,
                          color: task.isOverdue && !task.isCompleted
                              ? taskColors.overdueTaskText
                              : colorScheme.onSurface.withOpacity(0.6),
                        ),
                        const SizedBox(width: 4),
                        Text(
                          'Due: ${_formatDateTime(task.dueDate, task.dueTime)}',
                          style: TextStyle(
                            fontSize: 12,
                            color: task.isOverdue && !task.isCompleted
                                ? taskColors.overdueTaskText
                                : colorScheme.onSurface.withOpacity(0.6),
                          ),
                        ),
                      ],
                    ),
                    // Completion date (only for completed tasks)
                    if (showCompletionDate && task.completedDate != null) ...
                      [
                        const SizedBox(height: 2),
                        Row(
                          children: [
                            Icon(
                              Icons.check_circle,
                              size: 16,
                              color: colorScheme.primary,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              'Completed: ${_formatDate(task.completedDate!)}',
                              style: TextStyle(
                                fontSize: 12,
                                color: colorScheme.primary,
                              ),
                            ),
                          ],
                        ),
                      ],
                    // Overdue indicator
                    if (task.isOverdue && !task.isCompleted) ...
                      [
                        const SizedBox(height: 2),
                        Row(
                          children: [
                            Icon(
                              Icons.warning,
                              size: 16,
                              color: taskColors.overdueTaskText,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              'OVERDUE',
                              style: TextStyle(
                                fontSize: 11,
                                fontWeight: FontWeight.bold,
                                color: taskColors.overdueTaskText,
                              ),
                            ),
                          ],
                        ),
                      ],
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
