import 'package:flutter/material.dart';
import '../data/database.dart';
import '../providers/theme_provider.dart';
import 'util/todo_tile.dart';

class CompletedTasksPage extends StatefulWidget {
  final ToDoDatabase database;

  const CompletedTasksPage({
    super.key,
    required this.database,
  });

  @override
  State<CompletedTasksPage> createState() => _CompletedTasksPageState();
}

class _CompletedTasksPageState extends State<CompletedTasksPage> {
  
  // Uncheck task (move back to pending)
  void uncheckTask(int taskIndex) {
    final completedTasks = widget.database.getCompletedTasks();
    if (taskIndex < completedTasks.length) {
      final task = completedTasks[taskIndex];
      final originalIndex = widget.database.toDoList.indexOf(task);
      
      setState(() {
        widget.database.toDoList[originalIndex].isCompleted = false;
        widget.database.toDoList[originalIndex].completedDate = null;
      });
      widget.database.updateDatabase();
    }
  }

  // Delete completed task permanently
  void deleteTask(int taskIndex) {
    final completedTasks = widget.database.getCompletedTasks();
    if (taskIndex < completedTasks.length) {
      final task = completedTasks[taskIndex];
      final originalIndex = widget.database.toDoList.indexOf(task);
      
      // Show confirmation dialog
      showDialog(
        context: context,
        builder: (BuildContext context) {
          return AlertDialog(
            title: Text('Delete Task'),
            content: Text('Are you sure you want to permanently delete "${task.name}"?'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: Text('Cancel'),
              ),
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  setState(() {
                    widget.database.toDoList.removeAt(originalIndex);
                  });
                  widget.database.updateDatabase();
                },
                style: TextButton.styleFrom(foregroundColor: Colors.red),
                child: Text('Delete'),
              ),
            ],
          );
        },
      );
    }
  }

  // Clear all completed tasks
  void clearAllCompleted() {
    final completedTasks = widget.database.getCompletedTasks();
    if (completedTasks.isEmpty) return;

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('Clear All Completed'),
          content: Text('Are you sure you want to permanently delete all ${completedTasks.length} completed tasks?'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text('Cancel'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                setState(() {
                  widget.database.toDoList.removeWhere((task) => task.isCompleted);
                });
                widget.database.updateDatabase();
              },
              style: TextButton.styleFrom(foregroundColor: Colors.red),
              child: Text('Delete All'),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final completedTasks = widget.database.getCompletedTasks();
    final taskColors = Theme.of(context).extension<TaskColors>()!;
    final theme = Theme.of(context);
    
    return Scaffold(
      backgroundColor: taskColors.completedBackground,
      appBar: AppBar(
        title: Text('Completed Tasks'),
        actions: [
          if (completedTasks.isNotEmpty) ...[
            PopupMenuButton<String>(
              onSelected: (value) {
                if (value == 'clear_all') {
                  clearAllCompleted();
                }
              },
              itemBuilder: (BuildContext context) => [
                PopupMenuItem<String>(
                  value: 'clear_all',
                  child: Row(
                    children: [
                      Icon(Icons.delete_sweep, color: Colors.red),
                      SizedBox(width: 8),
                      Text('Clear All'),
                    ],
                  ),
                ),
              ],
            ),
            Padding(
              padding: const EdgeInsets.only(right: 16.0),
              child: Center(
                child: Text(
                  '${completedTasks.length} done',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ),
          ],
        ],
      ),
      body: completedTasks.isEmpty
          ? Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.check_circle_outline,
                    size: 64,
                    color: Colors.grey[400],
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'No completed tasks yet',
                    style: TextStyle(
                      fontSize: 18,
                      color: Colors.grey[600],
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Complete some tasks to see them here',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[500],
                    ),
                  ),
                ],
              ),
            )
          : Column(
              children: [
                // Summary stats
                Container(
                  margin: EdgeInsets.all(16),
                  padding: EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: taskColors.statsBackground,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: taskColors.statsBorder, width: 1),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      Column(
                        children: [
                          Text(
                            '${completedTasks.length}',
                            style: TextStyle(
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                              color: theme.colorScheme.primary,
                            ),
                          ),
                          Text(
                            'Completed',
                            style: TextStyle(
                              color: theme.colorScheme.onSurface.withOpacity(0.7),
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                      Container(
                        height: 40,
                        width: 1,
                        color: taskColors.statsBorder,
                      ),
                      Column(
                        children: [
                          Text(
                            '${completedTasks.where((task) => task.completedDate != null && task.completedDate!.isAfter(DateTime.now().subtract(Duration(days: 7)))).length}',
                            style: TextStyle(
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                              color: theme.colorScheme.primary,
                            ),
                          ),
                          Text(
                            'This Week',
                            style: TextStyle(
                              color: theme.colorScheme.onSurface.withOpacity(0.7),
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                      Container(
                        height: 40,
                        width: 1,
                        color: taskColors.statsBorder,
                      ),
                      Column(
                        children: [
                          Text(
                            '${completedTasks.where((task) => task.completedDate != null && task.completedDate!.isAfter(DateTime.now().subtract(Duration(days: 1)))).length}',
                            style: TextStyle(
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                              color: theme.colorScheme.primary,
                            ),
                          ),
                          Text(
                            'Today',
                            style: TextStyle(
                              color: theme.colorScheme.onSurface.withOpacity(0.7),
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                // Completed tasks list
                Expanded(
                  child: ListView.builder(
                    itemCount: completedTasks.length,
                    itemBuilder: (context, index) {
                      return ToDoTile(
                        task: completedTasks[index],
                        onChanged: (value) => uncheckTask(index),
                        deleteFunction: (context) => deleteTask(index),
                        editFunction: (context) => {}, // No edit for completed tasks
                        showCompletionDate: true,
                      );
                    },
                  ),
                ),
              ],
            ),
    );
  }
}
