import 'package:hive_flutter/hive_flutter.dart';

class ToDoTask {
  String name;
  bool isCompleted;
  DateTime dueDate;
  DateTime? dueTime; // Optional time component
  DateTime? completedDate;

  ToDoTask({
    required this.name,
    required this.isCompleted,
    required this.dueDate,
    this.dueTime,
    this.completedDate,
  });

  // Convert to Map for Hive storage
  Map<String, dynamic> toMap() {
    return {
      'name': name,
      'isCompleted': isCompleted,
      'dueDate': dueDate.millisecondsSinceEpoch,
      'dueTime': dueTime?.millisecondsSinceEpoch,
      'completedDate': completedDate?.millisecondsSinceEpoch,
    };
  }

  // Create from Map for Hive loading
  factory ToDoTask.fromMap(Map<String, dynamic> map) {
    return ToDoTask(
      name: map['name'],
      isCompleted: map['isCompleted'],
      dueDate: DateTime.fromMillisecondsSinceEpoch(map['dueDate']),
      dueTime: map['dueTime'] != null 
          ? DateTime.fromMillisecondsSinceEpoch(map['dueTime']) 
          : null,
      completedDate: map['completedDate'] != null 
          ? DateTime.fromMillisecondsSinceEpoch(map['completedDate']) 
          : null,
    );
  }

  // Check if task is overdue
  bool get isOverdue {
    if (isCompleted) return false;
    final now = DateTime.now();
    final compareDate = dueTime ?? DateTime(dueDate.year, dueDate.month, dueDate.day, 23, 59, 59);
    return now.isAfter(compareDate);
  }

  // Get combined due DateTime
  DateTime get combinedDueDateTime {
    if (dueTime != null) {
      return DateTime(
        dueDate.year,
        dueDate.month,
        dueDate.day,
        dueTime!.hour,
        dueTime!.minute,
      );
    }
    return DateTime(dueDate.year, dueDate.month, dueDate.day, 23, 59, 59);
  }
}

class ToDoDatabase {
  List<ToDoTask> toDoList = [];
  // Reference the hive database
  final _myBox = Hive.box('mybox');

  // run this method if this is the 1st time ever opening this app
  void createInitialData() {
    final now = DateTime.now();
    toDoList = [
      ToDoTask(
        name: "Make a video",
        isCompleted: false,
        dueDate: now.add(Duration(days: 1)),
      ),
      ToDoTask(
        name: "Do the dishes",
        isCompleted: false,
        dueDate: now.add(Duration(days: 2)),
        dueTime: DateTime(now.year, now.month, now.day, 18, 0), // 6 PM
      ),
      ToDoTask(
        name: "Clean the room",
        isCompleted: false,
        dueDate: now.add(Duration(days: 3)),
      ),
    ];
  }

  // Load the data from database
  void loadData() {
    final data = _myBox.get("TODOLIST");
    if (data != null) {
      if (data is List && data.isNotEmpty && data[0] is Map) {
        // New format with ToDoTask objects
        toDoList = data.map((item) => ToDoTask.fromMap(Map<String, dynamic>.from(item))).toList();
      } else {
        // Legacy format - convert old tasks
        _convertLegacyData(data);
      }
    }
  }

  // Convert legacy data format to new format
  void _convertLegacyData(dynamic data) {
    final now = DateTime.now();
    toDoList = [];
    
    if (data is List) {
      for (var item in data) {
        if (item is List && item.length >= 2) {
          toDoList.add(ToDoTask(
            name: item[0].toString(),
            isCompleted: item[1] as bool,
            dueDate: now.add(Duration(days: 1)), // Default due date
          ));
        }
      }
    }
    // Save converted data
    updateDatabase();
  }

  // Update the database
  void updateDatabase() {
    final data = toDoList.map((task) => task.toMap()).toList();
    _myBox.put("TODOLIST", data);
  }

  // Get pending tasks (not completed) sorted by due date/time
  List<ToDoTask> getPendingTasks() {
    final pending = toDoList.where((task) => !task.isCompleted).toList();
    pending.sort((a, b) => a.combinedDueDateTime.compareTo(b.combinedDueDateTime));
    return pending;
  }

  // Get completed tasks sorted by completion date (most recent first)
  List<ToDoTask> getCompletedTasks() {
    final completed = toDoList.where((task) => task.isCompleted).toList();
    completed.sort((a, b) {
      if (a.completedDate == null && b.completedDate == null) return 0;
      if (a.completedDate == null) return 1;
      if (b.completedDate == null) return -1;
      return b.completedDate!.compareTo(a.completedDate!);
    });
    return completed;
  }
}
